"""Create target and picture

Revision ID: 8fd03ac6d77d
Revises:
Create Date: 2021-05-08 20:05:59.130362

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "8fd03ac6d77d"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "targets",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("first_name", sa.String(), nullable=True),
        sa.Column("last_name", sa.String(), nullable=True),
        sa.Column("age", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_targets_id"), "targets", ["id"], unique=False)
    op.create_table(
        "pictures",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.Column("path", sa.String(), nullable=True),
        sa.Column("target_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["target_id"],
            ["targets.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_pictures_id"), "pictures", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_pictures_id"), table_name="pictures")
    op.drop_table("pictures")
    op.drop_index(op.f("ix_targets_id"), table_name="targets")
    op.drop_table("targets")
    # ### end Alembic commands ###
