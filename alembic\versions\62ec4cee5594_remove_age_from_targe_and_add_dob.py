"""Remove age from targe and add DOB

Revision ID: 62ec4cee5594
Revises: 8fd03ac6d77d
Create Date: 2021-05-10 13:12:58.017082

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '62ec4cee5594'
down_revision = '8fd03ac6d77d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('targets', sa.Column('dob', sa.Date(), nullable=True))
    op.drop_column('targets', 'age')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('targets', sa.Column('age', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_column('targets', 'dob')
    # ### end Alembic commands ###
